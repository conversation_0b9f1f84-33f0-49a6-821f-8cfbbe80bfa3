package corp.jamaro.jamaroservidor.app.repository;

import corp.jamaro.jamaroservidor.app.model.Cliente;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface ClienteRepository extends ReactiveNeo4jRepository<Cliente, UUID> {

    /**
     * Busca clientes usando expresión regular en nombre, apellido o razónSocial.
     * Búsqueda insensible a mayúsculas y minúsculas, sin importar posición.
     * Limitado a 30 resultados para optimizar rendimiento.
     *
     * @param regex Expresión regular para la búsqueda
     * @return Flux de hasta 30 clientes que coinciden con la búsqueda
     */
    @Query("""
           MATCH (c:Cliente)
           WHERE c.estado = true
           AND (c.nombre =~ $regex OR c.apellido =~ $regex OR c.razonSocial =~ $regex)
           RETURN c
           LIMIT 30
           """)
    Flux<Cliente> findByNameRegex(@Param("regex") String regex);

    /**
     * Busca clientes por coincidencia exacta en dni, ruc o otroDocumento.
     * Búsqueda insensible a mayúsculas y minúsculas.
     *
     * @param regex Expresión regular para coincidencia exacta del documento
     * @return Flux de clientes que coinciden exactamente con el documento
     */
    @Query("""
           MATCH (c:Cliente)
           WHERE c.estado = true
           AND (c.dni =~ $regex OR c.ruc =~ $regex OR c.otroDocumento =~ $regex)
           RETURN c
           """)
    Flux<Cliente> findByDocumentExact(@Param("regex") String regex);

    /**
     * Actualiza los campos de un cliente existente de manera optimizada.
     * Solo actualiza los campos que no son null en los parámetros.
     *
     * @param id ID del cliente a actualizar
     * @param nombre Nuevo nombre (opcional)
     * @param apellido Nuevo apellido (opcional)
     * @param razonSocial Nueva razón social (opcional)
     * @param dni Nuevo DNI (opcional)
     * @param ruc Nuevo RUC (opcional)
     * @param otroDocumento Nuevo otro documento (opcional)
     * @param direccion Nueva dirección (opcional)
     * @param telefono Nuevo teléfono (opcional)
     * @param email Nuevo email (opcional)
     * @param tieneCredito Nuevo estado de crédito (opcional)
     * @param esMayorista Nuevo estado mayorista (opcional)
     * @param estado Nuevo estado (opcional)
     * @param metadata Nueva metadata (opcional)
     * @return Mono que completa cuando la operación termina
     */
    @Query("""
           MATCH (c:Cliente) WHERE c.id = $id
           SET c.nombre = CASE WHEN $nombre IS NOT NULL THEN $nombre ELSE c.nombre END,
               c.apellido = CASE WHEN $apellido IS NOT NULL THEN $apellido ELSE c.apellido END,
               c.razonSocial = CASE WHEN $razonSocial IS NOT NULL THEN $razonSocial ELSE c.razonSocial END,
               c.dni = CASE WHEN $dni IS NOT NULL THEN $dni ELSE c.dni END,
               c.ruc = CASE WHEN $ruc IS NOT NULL THEN $ruc ELSE c.ruc END,
               c.otroDocumento = CASE WHEN $otroDocumento IS NOT NULL THEN $otroDocumento ELSE c.otroDocumento END,
               c.direccion = CASE WHEN $direccion IS NOT NULL THEN $direccion ELSE c.direccion END,
               c.telefono = CASE WHEN $telefono IS NOT NULL THEN $telefono ELSE c.telefono END,
               c.email = CASE WHEN $email IS NOT NULL THEN $email ELSE c.email END,
               c.tieneCredito = CASE WHEN $tieneCredito IS NOT NULL THEN $tieneCredito ELSE c.tieneCredito END,
               c.esMayorista = CASE WHEN $esMayorista IS NOT NULL THEN $esMayorista ELSE c.esMayorista END,
               c.estado = CASE WHEN $estado IS NOT NULL THEN $estado ELSE c.estado END,
               c.metadata = CASE WHEN $metadata IS NOT NULL THEN $metadata ELSE c.metadata END
           """)
    Mono<Void> updateClienteFields(
            @Param("id") UUID id,
            @Param("nombre") String nombre,
            @Param("apellido") String apellido,
            @Param("razonSocial") String razonSocial,
            @Param("dni") String dni,
            @Param("ruc") String ruc,
            @Param("otroDocumento") String otroDocumento,
            @Param("direccion") String direccion,
            @Param("telefono") String telefono,
            @Param("email") String email,
            @Param("tieneCredito") Boolean tieneCredito,
            @Param("esMayorista") Boolean esMayorista,
            @Param("estado") Boolean estado,
            @Param("metadata") String metadata
    );
}
