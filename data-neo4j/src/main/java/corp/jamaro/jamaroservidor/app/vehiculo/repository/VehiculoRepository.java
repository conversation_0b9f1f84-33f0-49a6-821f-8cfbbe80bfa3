package corp.jamaro.jamaroservidor.app.vehiculo.repository;

import corp.jamaro.jamaroservidor.app.vehiculo.model.Vehiculo;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface VehiculoRepository extends ReactiveNeo4jRepository<Vehiculo, UUID> {

    @Query("MATCH (vehiculo:Vehiculo)-[:CON_NOMBRE_DE_VEHICULO]->(nombre:VehiculoNombre {id: $nombreId}) RETURN vehiculo")
    Mono<Vehiculo> findByVehiculoNombreId(@Param("nombreId") UUID nombreId);

    @Query("""
            MATCH (v:Vehiculo)-[:CREADO_ACTUALIZADO_POR_EL_USUARIO]->(u:User {id: $userId})
            WHERE NOT ( (v)-[:CON_VEHICULO_MARCA]->(:VehiculoMarca) )
               OR NOT ( (v)-[:CON_NOMBRE_DE_VEHICULO]->(:VehiculoNombre) )
            RETURN v.id
            """)
    Flux<UUID> findIncompleteVehiculoIdsByUserId(@Param("userId") UUID userId);

}
