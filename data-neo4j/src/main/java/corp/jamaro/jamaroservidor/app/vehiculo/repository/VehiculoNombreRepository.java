package corp.jamaro.jamaroservidor.app.vehiculo.repository;

import corp.jamaro.jamaroservidor.app.vehiculo.model.VehiculoNombre;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.util.UUID;


@Repository
public interface VehiculoNombreRepository extends ReactiveNeo4jRepository<VehiculoNombre, UUID> {

    @Query("""
        MATCH (vn:VehiculoNombre)
        WHERE vn.nombre =~ $regex
        RETURN vn
        LIMIT 30
        """)
    Flux<VehiculoNombre> findByNombreRegex(@Param("regex") String regex);

}
