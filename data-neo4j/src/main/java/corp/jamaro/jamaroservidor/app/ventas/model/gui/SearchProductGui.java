package corp.jamaro.jamaroservidor.app.ventas.model.gui;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Node;

import org.springframework.data.neo4j.core.schema.Relationship;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Entidad que representa la configuración de la GUI para la búsqueda de productos.
 * Se “aplana” para evitar sub-entidades sin @Id:
 * - El grupo se representa por un campo simple (idGrupo).
 * - Los filtros se almacenan como relaciones (FiltroDatoRellenado) que incluyen fila, columna y dato.
 */
@Data
@Node
public class SearchProductGui {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    // Campo simple para identificar el grupo asociado (por ejemplo, la abreviatura o id)
    private String idGrupo;

    private String descripcion;
    private String codProductoOld;
    private String codFabricaOld;
    private String vehiculoSearch;

    /**
     * Relaciones que contienen cada “celda” de filtro.
     * Cada FiltroDatoRellenado almacena la referencia al Filtro (como target node),
     * la posición (fila y columna) y el dato ingresado.
     */
    @Relationship(type = "CON_FILTROS_CARGADOS")
    private List<FiltroDatoRellenado> filtroDatoRellenados;

    // Fecha de creación o última actualización.
    private Instant createdAt = Instant.now();

}
