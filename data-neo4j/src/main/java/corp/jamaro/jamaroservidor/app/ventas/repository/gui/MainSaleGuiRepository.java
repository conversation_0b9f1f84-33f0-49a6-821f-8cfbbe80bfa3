package corp.jamaro.jamaroservidor.app.ventas.repository.gui;

import corp.jamaro.jamaroservidor.app.ventas.model.gui.MainSaleGui;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Repositorio Reactivo para MainSaleGui.
 * Maneja operaciones de lectura y escritura específicas
 * sobre nodos MainSaleGui y sus relaciones con SaleGui.
 */
@Repository
public interface MainSaleGuiRepository extends ReactiveNeo4jRepository<MainSaleGui, UUID> {

    /**
     * Consulta que retorna únicamente el identificador (id) del MainSaleGui asociado al username dado.
     *
     * @param username El nombre de usuario del propietario.
     * @return Mono que emite el UUID del MainSaleGui o vacío si no se encuentra.
     */
    @Query("""
           MATCH (m:MainSaleGui)
           WHERE m.usernameOwner = $username
           RETURN m.id AS id
           """)
    Mono<UUID> findMainSaleGuiIdByUsername(@Param("username") String username);

    /**
     * Carga un MainSaleGui por su ID con todas sus relaciones a SaleGui.
     * Esta consulta es más eficiente que usar findById ya que carga exactamente lo necesario.
     *
     * @param mainSaleGuiId El UUID del MainSaleGui a cargar.
     * @return Mono que emite el MainSaleGui con sus relaciones o vacío si no se encuentra.
     */
    @Query("""
           MATCH (m:MainSaleGui) WHERE m.id = $mainSaleGuiId
           OPTIONAL MATCH (m)-[r:CON_SALE_GUI]->(s:SaleGui)
           RETURN m, collect(r), collect(s)
           """)
    Mono<MainSaleGui> findMainSaleGuiWithRelationsById(@Param("mainSaleGuiId") UUID mainSaleGuiId);

    /**
     * Crea una relación entre un MainSaleGui y un SaleGui utilizando los parámetros individuales.
     * Esta consulta es más eficiente que usar save() ya que solo actualiza los campos necesarios.
     *
     * @param mainSaleGuiId El UUID del MainSaleGui.
     * @param saleGuiId El UUID del SaleGui.
     * @param ordenPresentacion El orden de presentación.
     * @param dividerX La posición X del divisor.
     * @param dividerY La posición Y del divisor.
     * @param agregadoEl Timestamp de cuando fue agregado.
     * @return Mono que completa cuando la operación termina.
     */
    @Query("""
           MATCH (m:MainSaleGui) WHERE m.id = $mainSaleGuiId
           MATCH (s:SaleGui) WHERE s.id = $saleGuiId
           CREATE (m)-[r:CON_SALE_GUI {
               ordenPresentacion: $ordenPresentacion,
               dividerX: $dividerX,
               dividerY: $dividerY,
               agregadoEl: $agregadoEl
           }]->(s)
           """)
    Mono<Void> addSaleGuiRelation(
            @Param("mainSaleGuiId") UUID mainSaleGuiId,
            @Param("saleGuiId") UUID saleGuiId,
            @Param("ordenPresentacion") Integer ordenPresentacion,
            @Param("dividerX") Float dividerX,
            @Param("dividerY") Float dividerY,
            @Param("agregadoEl") Instant agregadoEl
    );

    /**
     * Elimina la relación entre un MainSaleGui y un SaleGui.
     *
     * @param mainSaleGuiId El UUID del MainSaleGui.
     * @param saleGuiId El UUID del SaleGui.
     * @return Mono que completa cuando la operación termina.
     */
    @Query("""
           MATCH (m:MainSaleGui)-[r:CON_SALE_GUI]->(s:SaleGui)
           WHERE m.id = $mainSaleGuiId AND s.id = $saleGuiId
           DELETE r
           """)
    Mono<Void> removeSaleGuiRelation(
            @Param("mainSaleGuiId") UUID mainSaleGuiId,
            @Param("saleGuiId") UUID saleGuiId
    );

    /**
     * Actualiza el orden de presentación de una relación específica entre MainSaleGui y SaleGui.
     *
     * @param mainSaleGuiId El UUID del MainSaleGui.
     * @param saleGuiId El UUID del SaleGui.
     * @param ordenPresentacion El nuevo orden de presentación.
     * @return Mono que completa cuando la operación termina.
     */
    @Query("""
           MATCH (m:MainSaleGui)-[r:CON_SALE_GUI]->(s:SaleGui)
           WHERE m.id = $mainSaleGuiId AND s.id = $saleGuiId
           SET r.ordenPresentacion = $ordenPresentacion
           """)
    Mono<Void> updateOrdenPresentacion(
            @Param("mainSaleGuiId") UUID mainSaleGuiId,
            @Param("saleGuiId") UUID saleGuiId,
            @Param("ordenPresentacion") Integer ordenPresentacion
    );

    /**
     * Actualiza el orden de presentación de varias relaciones de un MainSaleGui.
     * Esta consulta es útil para reordenar las relaciones después de eliminar una.
     *
     * @param mainSaleGuiId El UUID del MainSaleGui.
     * @param ordenesMap Un mapa con los IDs de SaleGui como claves y los nuevos órdenes como valores.
     *                   Cada elemento debe contener: {"saleGuiId": UUID, "orden": Integer}.
     * @return Mono que completa cuando la operación termina.
     */
    @Query("""
           UNWIND $ordenesMap AS orden
           MATCH (m:MainSaleGui)-[r:CON_SALE_GUI]->(s:SaleGui)
           WHERE m.id = $mainSaleGuiId AND s.id = orden.saleGuiId
           SET r.ordenPresentacion = orden.orden
           """)
    Mono<Void> updateMultipleOrdenPresentacion(
            @Param("mainSaleGuiId") UUID mainSaleGuiId,
            @Param("ordenesMap") List<Map<String, Object>> ordenesMap
    );

    /**
     * Elimina todas las relaciones entre un MainSaleGui y todos sus SaleGui.
     *
     * @param mainSaleGuiId El UUID del MainSaleGui.
     * @return Mono que completa cuando la operación termina.
     */
    @Query("""
           MATCH (m:MainSaleGui)-[r:CON_SALE_GUI]->(s:SaleGui)
           WHERE m.id = $mainSaleGuiId
           DELETE r
           """)
    Mono<Void> removeAllSaleGuiRelations(@Param("mainSaleGuiId") UUID mainSaleGuiId);
}
