package corp.jamaro.jamaroservidor.app.ventas.model.gui;


import corp.jamaro.jamaroservidor.app.model.collaborative.CollaborativeRoom;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.*;

import java.time.Instant;
import java.util.*;


@Data
@Node
public class SaleGui {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "CON_COLLABORATIVE_ROOM")
    private CollaborativeRoom collaborativeRoom;

    @Relationship(type = "CON_BUSQUEDA_PRODUCTOS")
    private Set<SearchProductGui> searchProducts;

    @Relationship(type = "CON_VENTA")
    private Sale sale;

    private Instant createdAt = Instant.now();
}
/*
se programarán limpieza de estos
- Hacer una interfaz con las opciones del Sistema por ejemplouna que necesitaremos es eliminar
los SaleGui con su respectivo SearchProductGui y su Sale para los Sale con esProforma=true;
entonces necesitamos setear cada cuanto tiempo hacerlo, por ejemplo cada 1 semana.
 */
