package corp.jamaro.jamaroservidor.app.ventas.model.gui;

import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Set;
import java.util.UUID;

@Data
@Node
public class UniversalSaleGui {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(value = "PERTENECE_AL_USER")
    private User user;

    @Relationship(value = "CON_MAINSALEGUI")
    private MainSaleGui mainSaleGui; //main principal del User

    private Set<UUID> auditingMainSales;// MainSales de otros user que esta auditando

    //Lista de tareas asignadas

    //Lista de notas del mismo usuario (hacer luego la lógica de recordatorios con tiempo)

    //cualquier otra idea para la ventana principal de venta.

    private String guiConfig; // un json con las preferencias del usuario para su gui (color de usuario, tamaño letra, etc)


}
