package corp.jamaro.jamaroservidor.app.controller;

import corp.jamaro.jamaroservidor.app.model.Cliente;
import corp.jamaro.jamaroservidor.app.service.ClienteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;

import java.util.UUID;

/**
 * Controlador para gestionar las operaciones relacionadas con los clientes a través de RSocket.
 * Implementa búsquedas por nombre/apellido/razónSocial y por documentos (dni/ruc/otroDocumento).
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class ClienteController {

    private final ClienteService clienteService;

    /**
     * Permite suscribirse a actualizaciones de un Cliente específico.
     *
     * @param clienteId ID del Cliente al que se quiere suscribir
     * @return Flux que emite el Cliente actual y sus actualizaciones futuras
     */
    @MessageMapping("cliente.subscribe")
    public Flux<Cliente> subscribeToClienteUpdates(UUID clienteId) {
        log.info("Suscribiendo a actualizaciones del Cliente con ID: {}", clienteId);
        return clienteService.subscribeToClienteUpdates(clienteId);
    }

    /**
     * Busca clientes usando expresión regular en nombre, apellido o razónSocial.
     * Búsqueda insensible a mayúsculas y minúsculas, sin importar posición.
     * Limitado a 30 resultados para optimizar rendimiento.
     *
     * @param request Solicitud con el término de búsqueda
     * @return Flux de hasta 30 clientes que coinciden con la búsqueda
     */
    @MessageMapping("cliente.searchByName")
    public Flux<Cliente> searchClientesByName(SearchClientesByNameRequest request) {
        log.info("Buscando clientes por nombre/apellido/razónSocial con término: {}", request.searchTerm());
        return clienteService.searchClientesByNameRegex(request.searchTerm());
    }

    /**
     * Busca clientes por coincidencia exacta en dni, ruc o otroDocumento.
     * Búsqueda insensible a mayúsculas y minúsculas.
     *
     * @param request Solicitud con el documento a buscar
     * @return Flux de clientes que coinciden exactamente con el documento
     */
    @MessageMapping("cliente.searchByDocument")
    public Flux<Cliente> searchClientesByDocument(SearchClientesByDocumentRequest request) {
        log.info("Buscando clientes por documento exacto: {}", request.document());
        return clienteService.searchClientesByDocumentExact(request.document());
    }

    /**
     * Crea un nuevo cliente en la base de datos.
     *
     * @param cliente Cliente a crear
     * @return Mono que emite el Cliente creado con su ID generado
     */
    @MessageMapping("cliente.create")
    public Mono<Cliente> createCliente(Cliente cliente) {
        log.info("Creando nuevo cliente: {} {}", cliente.getNombre(), cliente.getApellido());
        return clienteService.createCliente(cliente);
    }

    /**
     * Actualiza un cliente existente de manera optimizada.
     * Solo actualiza los campos que no son null en el objeto cliente.
     *
     * @param request Solicitud con el ID del cliente y los campos a actualizar
     * @return Mono que emite el Cliente actualizado
     */
    @MessageMapping("cliente.update")
    public Mono<Cliente> updateCliente(UpdateClienteRequest request) {
        log.info("Actualizando cliente con ID: {}", request.clienteId());
        return clienteService.updateCliente(request.clienteId(), request.cliente());
    }

    // Records para los parámetros de las solicitudes

    /**
     * Datos para la solicitud de búsqueda de clientes por nombre/apellido/razónSocial.
     *
     * @param searchTerm Término de búsqueda para nombre, apellido o razónSocial
     */
    public record SearchClientesByNameRequest(String searchTerm) {}

    /**
     * Datos para la solicitud de búsqueda de clientes por documento.
     *
     * @param document Documento a buscar (dni, ruc o otroDocumento)
     */
    public record SearchClientesByDocumentRequest(String document) {}

    /**
     * Datos para la solicitud de actualización de cliente.
     *
     * @param clienteId ID del cliente a actualizar
     * @param cliente Cliente con los campos a actualizar (campos null se ignoran)
     */
    public record UpdateClienteRequest(UUID clienteId, Cliente cliente) {}
}
