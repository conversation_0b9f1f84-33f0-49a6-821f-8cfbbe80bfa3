package corp.jamaro.jamaroservidor.app.ventas.service.gui;

import corp.jamaro.jamaroservidor.app.model.collaborative.CollaborativeRoom;
import corp.jamaro.jamaroservidor.app.model.collaborative.repository.CollaborativeRoomRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.SaleGui;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroservidor.app.ventas.model.dto.SaleGuiDto;
import corp.jamaro.jamaroservidor.app.ventas.repository.gui.SaleGuiRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import corp.jamaro.jamaroservidor.app.model.collaborative.UserConnected;
import corp.jamaro.jamaroservidor.app.ventas.repository.gui.SearchProductGuiRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.SignalType;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SaleGuiService {

    private final SaleGuiRepository saleGuiRepository;
    private final SearchProductGuiRepository searchProductGuiRepository;
    private final CollaborativeRoomRepository collaborativeRoomRepository;

    // Sinks para notificar cambios en tiempo real (clave: id de SaleGui, de tipo UUID)
    private final ConcurrentHashMap<UUID, Sinks.Many<SaleGui>> sinks = new ConcurrentHashMap<>();

    /**
     * Obtiene o crea el Sink asociado a un SaleGui.
     */
    private Sinks.Many<SaleGui> getOrCreateSink(UUID saleGuiId) {
        return sinks.computeIfAbsent(saleGuiId, id -> Sinks.many().replay().latest());
    }

    /**
     * Emite una versión fresca de la entidad cargada con Cypher para evitar problemas
     * de carga parcial de entidades. No guarda la entidad, solo emite.
     *
     * @param saleGuiId El UUID de la entidad SaleGui a emitir
     * @return Mono<Void> que completa cuando la operación termina
     */
    private Mono<Void> emitOnce(UUID saleGuiId) {
        return saleGuiRepository.findSaleGuiWithAllRelationsById(saleGuiId)
            .doOnSuccess(freshEntity -> {
                if (freshEntity != null) {
                    var sink = getOrCreateSink(freshEntity.getId());
                    sink.tryEmitNext(freshEntity);
                    log.info("Emitiendo actualización para SaleGui con id: {}", freshEntity.getId());
                } else {
                    log.warn("No se pudo emitir actualización para SaleGui con id: {} (entidad no encontrada)", saleGuiId);
                }
            })
            .doOnError(error -> {
                log.error("Error al cargar SaleGui con id: {}: {}", saleGuiId, error.getMessage());
            })
            .then();
    }

    // ──────────────────────────────────────────────
    // Conversión de entidad a DTO
    // ──────────────────────────────────────────────

    private SaleGuiDto toDto(SaleGui saleGui) {
        SaleGuiDto dto = new SaleGuiDto();
        dto.setId(saleGui.getId());
        dto.setCreatedAt(saleGui.getCreatedAt());

        // Obtener información del CollaborativeRoom
        if (saleGui.getCollaborativeRoom() != null) {
            dto.setIniciadaPor(saleGui.getCollaborativeRoom().getIniciadaPor());

            // Usuarios conectados desde CollaborativeRoom
            if (saleGui.getCollaborativeRoom().getUsersConnected() != null) {
                List<SaleGuiDto.ConnectedUserDto> connected = saleGui.getCollaborativeRoom().getUsersConnected().stream()
                        .map(uc -> new SaleGuiDto.ConnectedUserDto(
                                uc.getUser() != null ? uc.getUser().getUsername() : null,
                                uc.getIsOnline(),
                                uc.getLastConnectionAt(),
                                uc.getLastDisconnectionAt(),
                                uc.getUserGuiPreferences()
                        ))
                        .collect(Collectors.toList());
                dto.setUsersConnected(connected);
            }
        }
        // searchProductIds ordenados por createdAt (asc)
        if (saleGui.getSearchProducts() != null) {
            List<UUID> searchProductIds = saleGui.getSearchProducts().stream()
                    .sorted(Comparator.comparing(SearchProductGui::getCreatedAt))
                    .map(SearchProductGui::getId)
                    .collect(Collectors.toList());
            dto.setSearchProductIds(searchProductIds);
        }
        // saleId (del objeto Sale asociado)
        if (saleGui.getSale() != null) {
            dto.setSaleId(saleGui.getSale().getId());
        }

        // collaborativeRoomId (del objeto CollaborativeRoom asociado)
        if (saleGui.getCollaborativeRoom() != null) {
            dto.setCollaborativeRoomId(saleGui.getCollaborativeRoom().getId());
        }
        return dto;
    }

    // ──────────────────────────────────────────────
    // Suscripción y conexión de usuarios
    // ──────────────────────────────────────────────

    /**
     * Se suscribe a los cambios de un SaleGui. Antes de iniciar la suscripción se marca al usuario
     * como conectado (se agrega o actualiza su UserConnected) y, al finalizar la suscripción, se marca como desconectado.
     */
    public Flux<SaleGuiDto> subscribeToChanges(UUID saleGuiId) {
        return saleGuiRepository.findSaleGuiWithAllRelationsById(saleGuiId)
                .flatMapMany(saleGui -> SecurityUtils.getCurrentUser()
                        .flatMapMany(user -> {
                            // Verificar que el SaleGui tenga un CollaborativeRoom
                            if (saleGui.getCollaborativeRoom() == null) {
                                return Mono.error(new IllegalStateException("SaleGui no tiene CollaborativeRoom asociado"));
                            }

                            // Inicializar CollaborativeRoom si es necesario
                            CollaborativeRoom room = saleGui.getCollaborativeRoom();

                            // Inicializar usersConnected si es necesario
                            if (room.getUsersConnected() == null) {
                                room.setUsersConnected(new HashSet<>());
                            }

                            // Buscar si el usuario ya está conectado
                            Optional<UserConnected> existingUser = room.getUsersConnected().stream()
                                    .filter(uc -> uc.getUser() != null && uc.getUser().getId().equals(user.getId()))
                                    .findFirst();

                            // Actualizar o crear el UserConnected
                            if (existingUser.isPresent()) {
                                UserConnected uc = existingUser.get();
                                uc.setIsOnline(true);
                                uc.setLastConnectionAt(Instant.now());
                            } else {
                                UserConnected uc = new UserConnected();
                                uc.setUser(user);
                                uc.setIsOnline(true);
                                uc.setLastConnectionAt(Instant.now());
                                // Obtener clientType y clientVersion de SecurityUtils
                                Map<String, Object> metadata = SecurityUtils.getClientMetadata().block();
                                if (metadata != null) {
                                    uc.setClientType((String) metadata.get("clientType"));
                                    uc.setClientVersion((String) metadata.get("clientVersion"));
                                }
                                room.getUsersConnected().add(uc);
                            }

                            // Guardar el CollaborativeRoom actualizado
                            return collaborativeRoomRepository.save(room)
                                    .then(emitOnce(saleGui.getId()))
                                    .thenMany(Flux.defer(() ->
                                            getOrCreateSink(saleGui.getId()).asFlux()
                                                    .doFinally(signalType -> {
                                                        // Al finalizar la suscripción, marcar al usuario como desconectado
                                                        if (signalType == SignalType.CANCEL || signalType == SignalType.ON_ERROR || signalType == SignalType.ON_COMPLETE) {
                                                            log.info("Usuario desconectado de SaleGui: {}", saleGui.getId());
                                                            saleGuiRepository.findSaleGuiWithAllRelationsById(saleGui.getId())
                                                                    .flatMap(sg -> SecurityUtils.getCurrentUser()
                                                                            .flatMap(u -> {
                                                                                if (sg.getCollaborativeRoom() == null) {
                                                                                    return Mono.empty();
                                                                                }

                                                                                // Buscar el usuario conectado
                                                                                Set<UserConnected> ucSet = sg.getCollaborativeRoom().getUsersConnected();
                                                                                if (ucSet != null) {
                                                                                    ucSet.stream()
                                                                                            .filter(conn -> conn.getUser() != null && conn.getUser().getId().equals(u.getId()))
                                                                                            .findFirst()
                                                                                            .ifPresent(conn -> {
                                                                                                conn.setIsOnline(false);
                                                                                                conn.setLastDisconnectionAt(Instant.now());
                                                                                            });
                                                                                }

                                                                                // Guardar el CollaborativeRoom actualizado
                                                                                return collaborativeRoomRepository.save(sg.getCollaborativeRoom())
                                                                                        .then(emitOnce(sg.getId()));
                                                                            }))
                                                                    .subscribe(
                                                                        null,
                                                                        err -> log.error("Error al marcar desconexión", err)
                                                                    );
                                                        }
                                                    })
                                    ))
                                    .map(this::toDto);
                        })
                );
    }

    // ──────────────────────────────────────────────
    // Operaciones sobre SaleGui
    // ──────────────────────────────────────────────

    /**
     * Crea un nuevo SaleGui con un CollaborativeRoom asociado para el usuario actual.
     *
     * @return Mono que emite el SaleGuiDto creado
     */
    public Mono<SaleGuiDto> createSaleGuiForCurrentUser() {
        return SecurityUtils.getCurrentUser()
                .flatMap(user -> {
                    String username = user.getUsername();
                    log.info("Creating new SaleGui for user: {}", username);
                    return createSaleGui(username)
                            .map(this::toDto);
                });
    }

    /**
     * Crea un nuevo SaleGui con un CollaborativeRoom asociado.
     *
     * @param username El nombre de usuario que inicia el SaleGui
     * @return Mono que emite el SaleGui creado
     */
    public Mono<SaleGui> createSaleGui(String username) {
        // Crear un nuevo CollaborativeRoom
        CollaborativeRoom collaborativeRoom = new CollaborativeRoom();
        collaborativeRoom.setIniciadaPor(username);

        // Crear un nuevo SaleGui
        SaleGui saleGui = new SaleGui();
        saleGui.setCreatedAt(Instant.now());

        // Guardar ambos y establecer la relación
        return collaborativeRoomRepository.save(collaborativeRoom)
                .flatMap(savedRoom -> {
                    // Luego guardar el SaleGui
                    return saleGuiRepository.save(saleGui)
                            .flatMap(savedSaleGui -> {
                                // Finalmente establecer la relación
                                return saleGuiRepository.addCollaborativeRoomRelation(savedSaleGui.getId(), savedRoom.getId())
                                        .then(saleGuiRepository.findSaleGuiWithAllRelationsById(savedSaleGui.getId()));
                            });
                });
    }

    // ──────────────────────────────────────────────
    // Operaciones sobre SearchProductGui
    // ──────────────────────────────────────────────

    /**
     * Crea un nuevo SearchProductGui, lo añade al SaleGui y emite la actualización.
     */
    public Mono<Void> createNewSearchProduct(UUID saleGuiId) {
        log.info("Creando nuevo SearchProductGui para SaleGui con id: {}", saleGuiId);

        // Primero crear el SearchProductGui
        SearchProductGui sp = new SearchProductGui();
        sp.setCreatedAt(Instant.now());

        return searchProductGuiRepository.save(sp)
                .flatMap(savedSp -> {
                    log.info("SearchProductGui creado con id: {}", savedSp.getId());
                    // Luego establecer la relación con el SaleGui usando Cypher
                    return saleGuiRepository.addSearchProductGuiRelation(saleGuiId, savedSp.getId())
                            .doOnSuccess(v -> log.info("Relación creada entre SaleGui {} y SearchProductGui {}", saleGuiId, savedSp.getId()))
                            .doOnError(e -> log.error("Error al crear relación: {}", e.getMessage()))
                            .then(Mono.defer(() -> emitOnce(saleGuiId)));
                })
                .doOnError(e -> log.error("Error al crear SearchProductGui para SaleGui {}: {}", saleGuiId, e.getMessage()));
    }

    /**
     * Elimina un SearchProductGui del SaleGui y lo borra de la base de datos.
     */
    public Mono<Void> deleteSearchProduct(UUID saleGuiId, UUID searchProductId) {
        log.info("Eliminando SearchProductGui {} del SaleGui {}", searchProductId, saleGuiId);

        // Primero eliminar la relación usando Cypher
        return saleGuiRepository.removeSearchProductGuiRelation(saleGuiId, searchProductId)
                .doOnSuccess(v -> log.info("Relación eliminada entre SaleGui {} y SearchProductGui {}", saleGuiId, searchProductId))
                .doOnError(e -> log.error("Error al eliminar relación: {}", e.getMessage()))
                // Luego eliminar el SearchProductGui
                .then(searchProductGuiRepository.deleteById(searchProductId))
                .doOnSuccess(v -> log.info("SearchProductGui {} eliminado", searchProductId))
                .doOnError(e -> log.error("Error al eliminar SearchProductGui {}: {}", searchProductId, e.getMessage()))
                // Finalmente emitir la actualización
                .then(Mono.defer(() -> emitOnce(saleGuiId)))
                .doOnError(e -> log.error("Error al eliminar SearchProductGui {} del SaleGui {}: {}", searchProductId, saleGuiId, e.getMessage()));
    }
}
