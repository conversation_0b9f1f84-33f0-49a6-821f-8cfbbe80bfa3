package corp.jamaro.jamaroservidor.app.service;

import corp.jamaro.jamaroservidor.app.model.Cliente;
import corp.jamaro.jamaroservidor.app.repository.ClienteRepository;
import corp.jamaro.jamaroservidor.app.util.RegexUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.UUID;

/**
 * Servicio para gestionar las operaciones relacionadas con los clientes.
 * Este servicio implementa el patrón de comunicación RSocket donde:
 * 1. El cliente envía solicitudes de búsqueda
 * 2. El servidor procesa las solicitudes y devuelve los resultados
 * 3. El cliente puede suscribirse a actualizaciones de clientes específicos
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ClienteService {

    private final ClienteRepository clienteRepository;

    // Emisor para notificar actualizaciones vía RSocket
    // Utiliza multicast.directBestEffort() para enviar actualizaciones a múltiples suscriptores
    // sin bloquear si algún suscriptor es lento
    private final Sinks.Many<Cliente> updateSink = Sinks.many().multicast().directBestEffort();

    /**
     * Permite a los clientes suscribirse a actualizaciones de un Cliente específico.
     *
     * El flujo emitirá:
     * 1. El estado actual del Cliente como primer elemento
     * 2. Todas las actualizaciones futuras que se realicen sobre ese Cliente
     *
     * @param clienteId ID del Cliente al que se quiere suscribir
     * @return Flux que emite el Cliente actual y sus actualizaciones futuras
     */
    public Flux<Cliente> subscribeToClienteUpdates(UUID clienteId) {
        log.info("Suscribiendo a actualizaciones del Cliente con ID: {}", clienteId);

        // Primero obtenemos el estado actual del Cliente
        Mono<Cliente> currentState = clienteRepository.findById(clienteId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Cliente no encontrado con ID: " + clienteId)));

        // Luego nos suscribimos al flujo de actualizaciones, filtrando solo las del Cliente solicitado
        Flux<Cliente> updates = updateSink.asFlux()
                .filter(cliente -> cliente.getId().equals(clienteId));

        // Concatenamos el estado actual con las actualizaciones futuras
        return currentState.concatWith(updates);
    }

    /**
     * Busca clientes usando expresión regular en nombre, apellido o razónSocial.
     * Búsqueda insensible a mayúsculas y minúsculas, sin importar posición.
     * Limitado a 30 resultados para optimizar rendimiento.
     *
     * @param searchTerm Término de búsqueda para nombre, apellido o razónSocial
     * @return Flux de hasta 30 clientes que coinciden con la búsqueda
     */
    public Flux<Cliente> searchClientesByNameRegex(String searchTerm) {
        log.debug("Buscando clientes por nombre/apellido/razónSocial con término: {}", searchTerm);

        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            log.debug("Término de búsqueda vacío, retornando flujo vacío");
            return Flux.empty();
        }

        // Construir expresión regular usando RegexUtil para búsqueda que contenga todas las palabras
        String regex = RegexUtil.buildContainsAllRegex(searchTerm.trim());
        log.debug("Expresión regular generada: {}", regex);

        return clienteRepository.findByNameRegex(regex)
                .doOnNext(cliente -> log.debug("Cliente encontrado: {} {} - {}",
                    cliente.getNombre(), cliente.getApellido(), cliente.getRazonSocial()))
                .onErrorResume(e -> {
                    log.error("Error al buscar clientes por nombre: {}", e.getMessage());
                    return Flux.empty();
                });
    }

    /**
     * Busca clientes por coincidencia exacta en dni, ruc o otroDocumento.
     * Búsqueda insensible a mayúsculas y minúsculas.
     *
     * @param document Documento a buscar (dni, ruc o otroDocumento)
     * @return Flux de clientes que coinciden exactamente con el documento
     */
    public Flux<Cliente> searchClientesByDocumentExact(String document) {
        log.debug("Buscando clientes por documento exacto: {}", document);

        if (document == null || document.trim().isEmpty()) {
            log.debug("Documento de búsqueda vacío, retornando flujo vacío");
            return Flux.empty();
        }

        // Construir expresión regular usando RegexUtil para coincidencia exacta
        String regex = RegexUtil.buildExactMatchRegex(document.trim());
        log.debug("Expresión regular generada para documento: {}", regex);

        return clienteRepository.findByDocumentExact(regex)
                .doOnNext(cliente -> log.debug("Cliente encontrado por documento: {} - DNI: {}, RUC: {}, Otro: {}",
                    cliente.getNombre(), cliente.getDni(), cliente.getRuc(), cliente.getOtroDocumento()))
                .onErrorResume(e -> {
                    log.error("Error al buscar clientes por documento: {}", e.getMessage());
                    return Flux.empty();
                });
    }

    /**
     * Crea un nuevo cliente en la base de datos.
     * Utiliza Spring Data Neo4j para la persistencia optimizada.
     *
     * @param cliente Cliente a crear
     * @return Mono que emite el Cliente creado con su ID generado
     */
    public Mono<Cliente> createCliente(Cliente cliente) {
        log.info("Creando nuevo cliente: {} {}", cliente.getNombre(), cliente.getApellido());

        // Validaciones básicas
        if (cliente.getNombre() == null || cliente.getNombre().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("El nombre del cliente es requerido"));
        }

        // Asegurar que el ID sea null para que se genere automáticamente
        cliente.setId(null);

        // Establecer valores por defecto si no están definidos
        if (cliente.getTieneCredito() == null) {
            cliente.setTieneCredito(false);
        }
        if (cliente.getEsMayorista() == null) {
            cliente.setEsMayorista(false);
        }
        if (cliente.getEstado() == null) {
            cliente.setEstado(true);
        }

        return clienteRepository.save(cliente)
                .doOnSuccess(savedCliente -> {
                    log.info("Cliente creado exitosamente con ID: {}", savedCliente.getId());
                    updateSink.tryEmitNext(savedCliente);
                })
                .doOnError(e -> log.error("Error al crear cliente: {}", e.getMessage()));
    }

    /**
     * Actualiza un cliente existente de manera optimizada.
     * Solo actualiza los campos que no son null en el objeto cliente.
     *
     * @param clienteId ID del cliente a actualizar
     * @param cliente Cliente con los campos a actualizar (campos null se ignoran)
     * @return Mono que emite el Cliente actualizado
     */
    public Mono<Cliente> updateCliente(UUID clienteId, Cliente cliente) {
        log.info("Actualizando cliente con ID: {}", clienteId);

        if (clienteId == null) {
            return Mono.error(new IllegalArgumentException("El ID del cliente es requerido"));
        }

        // Verificar que el cliente existe
        return clienteRepository.existsById(clienteId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Cliente no encontrado con ID: " + clienteId));
                    }

                    // Usar la consulta optimizada para actualizar solo campos no null
                    return clienteRepository.updateClienteFields(
                            clienteId,
                            cliente.getNombre(),
                            cliente.getApellido(),
                            cliente.getRazonSocial(),
                            cliente.getDni(),
                            cliente.getRuc(),
                            cliente.getOtroDocumento(),
                            cliente.getDireccion(),
                            cliente.getTelefono(),
                            cliente.getEmail(),
                            cliente.getTieneCredito(),
                            cliente.getEsMayorista(),
                            cliente.getEstado(),
                            cliente.getMetadata()
                    );
                })
                .then(clienteRepository.findById(clienteId))
                .doOnSuccess(updatedCliente -> {
                    log.info("Cliente actualizado exitosamente: {}", clienteId);
                    updateSink.tryEmitNext(updatedCliente);
                })
                .doOnError(e -> log.error("Error al actualizar cliente {}: {}", clienteId, e.getMessage()));
    }

    /**
     * Emite el estado actual del Cliente con todas sus relaciones.
     * Método auxiliar para notificar actualizaciones a los suscriptores.
     *
     * @param clienteId ID del Cliente a emitir
     * @return Mono<Void> que completa cuando la operación termina
     */
    private Mono<Void> emitCliente(UUID clienteId) {
        return clienteRepository.findById(clienteId)
                .doOnNext(freshCliente -> {
                    log.debug("Emitiendo actualización para Cliente con ID: {}", clienteId);
                    updateSink.tryEmitNext(freshCliente);
                })
                .then();
    }
}
