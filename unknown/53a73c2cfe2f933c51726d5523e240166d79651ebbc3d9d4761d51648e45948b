package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.NombreGrupo;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.util.UUID;

@Repository
public interface NombreGrupoRepository extends ReactiveNeo4jRepository<NombreGrupo, UUID> {

    /**
     * Busca los nodos NombreGrupo cuyo campo 'nombre' coincide con la expresión regular dada.
     * La expresión regular debe tener la sintaxis compatible con Neo4j.
     *
     * @param regex Expresión regular para filtrar el nombre.
     * @return Un Flux con los NombreGrupo que cumplen el criterio.
     */
    @Query("MATCH (ng:NombreGrupo) WHERE ng.nombre =~ $regex RETURN ng LIMIT 33")
    Flux<NombreGrupo> findNombresGrupoByNombre(@Param("regex") String regex);
}
