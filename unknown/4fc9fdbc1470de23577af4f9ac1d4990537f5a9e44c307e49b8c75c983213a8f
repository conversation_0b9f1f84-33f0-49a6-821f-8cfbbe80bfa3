package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;

@Repository
public interface ItemRepository extends ReactiveNeo4jRepository<Item, UUID> {

    // Obtener un Item completo por codCompuesto con todas sus relaciones
    @Query("""
            MATCH (i:Item {codCompuesto: $codCompuesto})
            OPTIONAL MATCH (i)-[rcf:CON_CODIGO_FABRICA]->(cf:CodigoFabrica)
            OPTIONAL MATCH (i)-[rm:CON_MARCA]->(m:Marca)
            OPTIONAL MATCH (i)-[ra:CON_ATRIBUTO]->(a:Atributo)
            OPTIONAL MATCH (a)-[rf:PERTENECE_AL_FILTRO]->(f:Filtro)
            OPTIONAL MATCH (i)-[ru:CON_CODIGO_FABRICA]->(u:Ubicacion)
            RETURN i, collect(rcf), collect(cf), collect(rm), collect(m),
                   collect(ra), collect(a), collect(rf), collect(f),
                   collect(ru), collect(u)
            """)
    Mono<Item> findByCodCompuesto(@Param("codCompuesto") String codCompuesto);

    // Obtener Items cuya descripción cumpla con el regex (ignora mayúsculas/minúsculas y posición) con todas sus relaciones
    @Query("""
            MATCH (i:Item) WHERE i.descripcion =~ $regex
            OPTIONAL MATCH (i)-[rcf:CON_CODIGO_FABRICA]->(cf:CodigoFabrica)
            OPTIONAL MATCH (i)-[rm:CON_MARCA]->(m:Marca)
            OPTIONAL MATCH (i)-[ra:CON_ATRIBUTO]->(a:Atributo)
            OPTIONAL MATCH (a)-[rf:PERTENECE_AL_FILTRO]->(f:Filtro)
            OPTIONAL MATCH (i)-[ru:CON_CODIGO_FABRICA]->(u:Ubicacion)
            RETURN i, collect(rcf), collect(cf), collect(rm), collect(m),
                   collect(ra), collect(a), collect(rf), collect(f),
                   collect(ru), collect(u)
            LIMIT 30
            """)
    Flux<Item> findByDescripcionRegex(@Param("regex") String regex);

    // Obtener Items asociados a un Producto mediante la relación ITEM_PARTE_DEL_PRODUCTO con todas sus relaciones
    @Query("""
            MATCH (i:Item)-[:ITEM_PARTE_DEL_PRODUCTO]->(p:Producto {id: $productoId})
            OPTIONAL MATCH (i)-[rcf:CON_CODIGO_FABRICA]->(cf:CodigoFabrica)
            OPTIONAL MATCH (i)-[rm:CON_MARCA]->(m:Marca)
            OPTIONAL MATCH (i)-[ra:CON_ATRIBUTO]->(a:Atributo)
            OPTIONAL MATCH (a)-[rf:PERTENECE_AL_FILTRO]->(f:Filtro)
            OPTIONAL MATCH (i)-[ru:CON_CODIGO_FABRICA]->(u:Ubicacion)
            RETURN i, collect(rcf), collect(cf), collect(rm), collect(m),
                   collect(ra), collect(a), collect(rf), collect(f),
                   collect(ru), collect(u)
            """)
    Flux<Item> findByProductoId(@Param("productoId") UUID productoId);

    // Consulta para obtener los primeros 30 items con todas sus relaciones
    @Query("""
            MATCH (i:Item)
            OPTIONAL MATCH (i)-[rcf:CON_CODIGO_FABRICA]->(cf:CodigoFabrica)
            OPTIONAL MATCH (i)-[rm:CON_MARCA]->(m:Marca)
            OPTIONAL MATCH (i)-[ra:CON_ATRIBUTO]->(a:Atributo)
            OPTIONAL MATCH (a)-[rf:PERTENECE_AL_FILTRO]->(f:Filtro)
            OPTIONAL MATCH (i)-[ru:CON_CODIGO_FABRICA]->(u:Ubicacion)
            RETURN i, collect(rcf), collect(cf), collect(rm), collect(m),
                   collect(ra), collect(a), collect(rf), collect(f),
                   collect(ru), collect(u)
            LIMIT 30
            """)
    Flux<Item> findFirst30();

    // Consulta para buscar items por IDs con todas sus relaciones
    @Query("""
            MATCH (i:Item)
            WHERE i.id IN $ids
            OPTIONAL MATCH (i)-[rcf:CON_CODIGO_FABRICA]->(cf:CodigoFabrica)
            OPTIONAL MATCH (i)-[rm:CON_MARCA]->(m:Marca)
            OPTIONAL MATCH (i)-[ra:CON_ATRIBUTO]->(a:Atributo)
            OPTIONAL MATCH (a)-[rf:PERTENECE_AL_FILTRO]->(f:Filtro)
            OPTIONAL MATCH (i)-[ru:CON_CODIGO_FABRICA]->(u:Ubicacion)
            RETURN i, collect(rcf), collect(cf), collect(rm), collect(m),
                   collect(ra), collect(a), collect(rf), collect(f),
                   collect(ru), collect(u)
            """)
    Flux<Item> findItemsByIds(@Param("ids") List<String> ids);

    /**
     * Verifica si existe un Item con el codCompuesto especificado.
     *
     * @param codCompuesto Código compuesto del Item a verificar
     * @return true si existe, false en caso contrario
     */
    @Query("MATCH (i:Item {codCompuesto: $codCompuesto}) RETURN COUNT(i) > 0")
    Mono<Boolean> existsByCodCompuesto(@Param("codCompuesto") String codCompuesto);
}
