package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.Grupo;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface GrupoRepository extends ReactiveNeo4jRepository<Grupo, String> {

    /**
     * Busca el Grupo que tiene asociado un NombreGrupo con el id especificado.
     *
     * @param nombreGrupoId UUID del NombreGrupo.
     * @return Un Mono con el Grupo encontrado o vacío si no existe.
     */
    @Query("MATCH (g:Grupo)-[:CON_NOMBRE_GRUPO]->(ng:NombreGrupo {id: $nombreGrupoId}) RETURN g")
    Mono<Grupo> findGrupoByNombreGrupoId(@Param("nombreGrupoId") UUID nombreGrupoId);

    /**
     * Busca un Grupo por su id.
     *
     * @param id El id del Grupo.
     * @return Un Mono con el Grupo encontrado o vacío si no existe.
     */
    @Query("MATCH (g:Grupo {id: $id}) RETURN g")
    Mono<Grupo> findGrupoById(@Param("id") String id);
}
