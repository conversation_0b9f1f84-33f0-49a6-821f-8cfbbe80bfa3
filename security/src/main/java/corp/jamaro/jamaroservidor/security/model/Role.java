package corp.jamaro.jamaroservidor.security.model;

import corp.jamaro.jamaroservidor.security.enums.RoleEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;


/**
 * Representa un rol en el sistema.
 * Los roles se utilizan para agrupar permisos y facilitar la gestión de autorizaciones.
 */
@Data
@NoArgsConstructor
@Node("Role")
public class Role {

    @Id
    private RoleEnum roleName; // Usamos el Enum para asegurar consistencia

}
