plugins {
    id 'java-library'
    id 'io.spring.dependency-management' version '1.1.6'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.boot:spring-boot-dependencies:3.4.0"
    }
}

group = 'corp.jamaro'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter' // For @Service, @Value, etc.
    implementation 'io.projectreactor:reactor-core' // For Flux, DataBuffer
    api 'io.minio:minio:8.5.11' // Changed to api

    compileOnly 'org.projectlombok:lombok' // Version managed by Spring Boot's dependency management
    annotationProcessor 'org.projectlombok:lombok' // Version managed
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor' // For @Value, good practice

    // Add other dependencies previously in the main build.gradle that are ONLY used by minio components
}
